create table blind_box.activity
(
    id             bigint auto_increment
        primary key,
    title          varchar(128) charset utf8mb4 default ''                not null comment '活动标题',
    image          varchar(128) charset utf8mb4 default ''                not null comment '活动图片',
    original_price bigint                       default 0                 not null comment '原价',
    act_price      bigint                       default 0                 not null comment '活动价',
    act_status     tinyint                      default 0                 not null comment '活动状态 1预售 2现货 3售罄',
    act_type       tinyint                      default 1                 not null comment '活动类型 1盲盒',
    start_time     int                          default 0                 not null comment '活动开始时间',
    end_time       int                          default 0                 not null comment '活动结束时间',
    recommend      tinyint                      default 0                 not null comment '是否推荐 0未推荐 1推荐',
    sort           int                          default 0                 not null comment '排序',
    is_deleted     tinyint                      default 0                 not null comment '软删',
    remark         varchar(128) charset utf8mb4 default ''                not null comment '备注',
    tips           varchar(128) charset utf8mb4 default ''                not null comment '友情提示',
    config_json    text charset utf8mb4                                   not null comment '活动配置字段',
    created_at     timestamp                    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at     timestamp                    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '活动表' charset = utf8;

create table blind_box.activity_sale_calendar
(
    id         bigint unsigned auto_increment
        primary key,
    act_id     bigint unsigned  default 0                 not null comment '活动id',
    time_desc  varchar(64)      default ''                not null comment '时间标志说明',
    sort       int unsigned     default 0                 not null comment '排序值',
    is_deleted tinyint unsigned default 0                 not null,
    created_at timestamp        default CURRENT_TIMESTAMP not null,
    updated_at timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '活动发售日历表' charset = utf8;

create index idx_act_id
    on blind_box.activity_sale_calendar (act_id);

create table blind_box.activity_subscribe
(
    id         bigint unsigned auto_increment
        primary key,
    user_id    bigint unsigned  default 0                 not null comment '用户id',
    act_id     bigint unsigned  default 0                 not null comment '活动id',
    is_remind  tinyint unsigned default 0                 not null comment '状态:0 未通知, 1已通知',
    is_deleted tinyint unsigned default 0                 not null,
    created_at timestamp        default CURRENT_TIMESTAMP not null,
    updated_at timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '活动订阅表' charset = utf8;

create index idx_act_id
    on blind_box.activity_subscribe (act_id);

create index idx_uid
    on blind_box.activity_subscribe (user_id);

create table blind_box.admin_account
(
    id         int(11) unsigned auto_increment comment '用户名'
        primary key,
    name       varchar(64) collate utf8mb4_estonian_ci  default ''                not null comment '姓名',
    account    varchar(64)                                                        not null comment '登录账号',
    password   varchar(64) collate utf8mb4_estonian_ci  default ''                not null comment '密码',
    mobile     varchar(32) collate utf8mb4_estonian_ci  default ''                not null comment '手机号码',
    avatar     varchar(128) collate utf8mb4_estonian_ci default ''                not null comment '头像',
    role_id    int(11) unsigned                         default 0                 not null comment '角色ID',
    status     tinyint(4) unsigned                      default 0                 not null comment '状态: 0禁用, 1启用',
    sort       int(11) unsigned                         default 100               not null comment '排序',
    is_deleted tinyint(4) unsigned                      default 0                 not null comment '软删',
    created_at timestamp                                default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp                                default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '用户表';

create index idx_account
    on blind_box.admin_account (account);

create index idx_mobile
    on blind_box.admin_account (mobile);

create table blind_box.admin_area
(
    id            int(11) unsigned auto_increment
        primary key,
    pid           int(11) unsigned         default 0  not null comment '父ID',
    city_code     varchar(64) charset utf8 default '' not null comment '城市编码',
    district_code varchar(64) charset utf8            not null comment '地区编码',
    name          varchar(32) charset utf8            not null comment '地区名称',
    level         varchar(64) charset utf8            not null comment '行政区划级别',
    sort          smallint unsigned        default 0  not null comment '排序',
    is_deleted    tinyint(4) unsigned      default 0  not null
)
    comment '地区表';

create table blind_box.admin_log
(
    id         bigint unsigned auto_increment
        primary key,
    account_id bigint unsigned default 0                 not null comment '账号id',
    role_id    bigint unsigned default 0                 not null comment '角色id',
    method     varchar(32)     default ''                not null comment '请求方式',
    trace_id   varchar(32)     default ''                not null comment '请求id',
    url        varchar(64)     default ''                not null comment '请求url',
    client_ip  varchar(32)     default ''                not null comment '客户端ip',
    header     text                                      null comment '请求头',
    body       text                                      null comment '请求体',
    created_at timestamp       default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '后台全局操作日志表';

create table blind_box.admin_log_black
(
    id         int(11) unsigned auto_increment
        primary key,
    url        varchar(128) default ''                not null comment 'url',
    is_deleted tinyint      default 0                 not null comment '软删',
    created_at timestamp    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment 'log黑名单表';

create table blind_box.admin_menu
(
    id          int(11) unsigned auto_increment
        primary key,
    pid         int(11) unsigned    default 0                 not null comment '父ID',
    title       varchar(64)         default ''                not null comment '模块标题',
    icon        varchar(64)         default ''                not null comment 'icon',
    menu_type   tinyint(4) unsigned default 1                 not null comment '1 目录 2菜单 3按钮',
    component   varchar(128)        default ''                not null comment '前端页面路径',
    be_auth     varchar(128)        default ''                not null comment '后端权限标识',
    is_show     tinyint(4) unsigned default 1                 not null comment '是否显示 1显示 2隐藏',
    system_menu tinyint(4) unsigned default 0                 not null comment '是否为系统菜单,系统菜单不可删除',
    sort        int(11) unsigned    default 100               not null comment '排序',
    status      tinyint(4) unsigned default 1                 not null comment '状态 1启用 2禁用',
    level       tinyint             default 1                 not null comment '等级',
    path        varchar(128)        default ''                not null comment '前端路由路径',
    path_name   varchar(128)        default ''                not null comment '前端路由名称',
    redirect    varchar(128)        default ''                not null comment '前端重定向url',
    fe_auth     varchar(128)        default ''                not null comment '前端权限标识',
    is_root     tinyint(4) unsigned default 0                 not null comment '是否需要超管权限',
    keep_alive  tinyint(4) unsigned default 0                 not null comment '前端页面缓存 1缓存',
    `desc`      varchar(255)        default ''                not null comment '描述',
    is_deleted  tinyint(4) unsigned default 0                 not null comment '软删',
    created_at  timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at  timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '权限菜单表';

create table blind_box.admin_menu_backup
(
    id          int(11) unsigned auto_increment
        primary key,
    pid         int(11) unsigned    default 0                 not null comment '父ID',
    title       varchar(64)         default ''                not null comment '模块标题',
    icon        varchar(64)         default ''                not null comment 'icon',
    menu_type   tinyint(4) unsigned default 1                 not null comment '1 目录 2菜单 3按钮',
    component   varchar(128)        default ''                not null comment '前端页面路径',
    be_auth     varchar(128)        default ''                not null comment '后端权限标识',
    is_show     tinyint(4) unsigned default 1                 not null comment '是否显示 1显示 2隐藏',
    system_menu tinyint(4) unsigned default 0                 not null comment '是否为系统菜单,系统菜单不可删除',
    sort        int(11) unsigned    default 100               not null comment '排序',
    status      tinyint(4) unsigned default 1                 not null comment '状态 1启用 2禁用',
    level       tinyint             default 1                 not null comment '等级',
    path        varchar(128)        default ''                not null comment '前端路由路径',
    path_name   varchar(128)        default ''                not null comment '前端路由名称',
    redirect    varchar(128)        default ''                not null comment '前端重定向url',
    fe_auth     varchar(128)        default ''                not null comment '前端权限标识',
    is_root     tinyint(4) unsigned default 0                 not null comment '是否需要超管权限',
    keep_alive  tinyint(4) unsigned default 0                 not null comment '前端页面缓存 1缓存',
    `desc`      varchar(255)        default ''                not null comment '描述',
    is_deleted  tinyint(4) unsigned default 0                 not null comment '软删',
    created_at  timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at  timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '权限菜单表';

create table blind_box.admin_role
(
    id         int(11) unsigned auto_increment comment '角色id'
        primary key,
    name       varchar(64)         default ''                not null comment '角色名称',
    `desc`     varchar(255)        default ''                not null comment '角色描述',
    sort       int                 default 0                 not null comment '排序',
    status     tinyint(4) unsigned default 1                 not null comment '状态 1启用 2禁用',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '角色表';

create table blind_box.admin_role_menu
(
    role_id int(11) unsigned not null comment '角色id',
    menu_id int(11) unsigned not null comment '菜单id',
    primary key (role_id, menu_id),
    constraint udx_rid_mid
        unique (role_id, menu_id)
)
    comment '角色菜单关联表';

create table blind_box.box_action_detail
(
    id                 int auto_increment
        primary key,
    created_at         timestamp           default CURRENT_TIMESTAMP not null,
    updated_at         timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    is_deleted         tinyint(4) unsigned default 0                 not null,
    remark             varchar(255)                                  null,
    user_id            int                                           not null comment '用户id',
    active_id          int                                           not null comment '活动id',
    box_id             int                                           not null,
    box_no             varchar(255)                                  not null comment '盲盒编号 箱子编号',
    box_slot           varchar(255)                                  not null comment '盲盒槽位 位置',
    action_type        int                                           not null comment '产出类型 1-明确产出 2-预产出 3-预产出锁定',
    action_desc        varchar(255)                                  not null comment '产出描述',
    card_id            int                                           not null comment '道具卡id',
    goods_id           int                                           not null comment '赏品id',
    box_level          varchar(255)                                  not null comment '款式类型',
    user_lottery_num   int                 default 0                 not null comment '用户抽奖次数 用户N抽',
    active_lottery_num int                 default 0                 not null comment '活动抽奖次数 活动N抽',
    normal_lottery_num int                 default 0                 not null comment '普通抽奖次数',
    secret_lottery_num int                 default 0                 not null comment '隐藏抽奖次数',
    trade_id           int                 default 0                 not null comment '赏品编号',
    used_fee           int                 default 0                 not null comment '实际支付金额：分'
)
    comment '盲盒行为详情表' charset = utf8;

create index idx_box
    on blind_box.box_action_detail (box_id, box_slot);

create index idx_goods_id
    on blind_box.box_action_detail (goods_id);

create index idx_trade_id
    on blind_box.box_action_detail (trade_id);

create index idx_user_id
    on blind_box.box_action_detail (user_id);

create table blind_box.box_active_config
(
    id             int auto_increment
        primary key,
    active_id      int                 default 0                 not null comment '活动id',
    lottery_num    int                 default 0                 not null comment '箱内赏品数量',
    head_image     varchar(300)        default ''                not null comment '头图',
    location_image varchar(300)        default ''                not null comment '位置图',
    box_image      varchar(300)        default ''                not null comment '箱子图',
    level_text     varchar(300)        default ''                not null comment '款式统计文本',
    shake_num      int                 default 0                 not null comment '摇一摇次数',
    updated_at     timestamp           default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP,
    created_at     timestamp           default CURRENT_TIMESTAMP not null,
    is_deleted     tinyint(4) unsigned default 0                 not null
)
    comment '盲盒通用配置' charset = utf8;

create table blind_box.box_active_must_config
(
    id            int auto_increment
        primary key,
    active_id     int                 default 0                 not null comment '活动id',
    type          int                 default 0                 not null comment '类型 1 必出款式  2 单人多抽必中 3活动多抽必中',
    lottery_num   int                 default 0                 not null comment '抽数',
    out_level     varchar(100)        default ''                null comment '必中款式类型',
    not_out_goods varchar(500)        default ''                null comment '没获取的赏品',
    out_goods     varchar(500)        default ''                null comment '必出赏品',
    goods_num     int                 default 0                 not null comment '允许产出商品数量',
    is_deleted    tinyint(4) unsigned default 0                 not null comment '是否删除 1是 0否',
    updated_at    timestamp           default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP,
    created_at    timestamp           default CURRENT_TIMESTAMP not null
)
    comment '盲盒通用必中配置' charset = utf8;

create table blind_box.box_card_source
(
    id          int auto_increment
        primary key,
    source_name varchar(20)                                   not null,
    updated_at  timestamp           default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP,
    is_deleted  tinyint(4) unsigned default 0                 not null,
    created_at  timestamp           default CURRENT_TIMESTAMP not null
)
    comment '盲盒道具卡获取来源' charset = utf8;

create table blind_box.box_goods
(
    id          bigint unsigned auto_increment comment 'Primary Key'
        primary key,
    is_deleted  tinyint unsigned default 0                 not null comment 'Soft Delete',
    created_at  timestamp        default CURRENT_TIMESTAMP not null comment 'Creation Timestamp',
    updated_at  timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment 'Update Timestamp',
    active_id   bigint unsigned  default 0                 not null comment '活动id',
    goods_name  varchar(255)     default ''                not null comment '商品名称',
    goods_cover varchar(500)     default ''                not null comment '商品封面',
    spu_id      bigint unsigned  default 0                 not null comment '商品id',
    sku_id      bigint unsigned  default 0                 not null comment '商品id',
    level_id    bigint unsigned  default 0                 not null comment '级别id',
    level_name  varchar(255)                               not null comment '级别名称',
    sort        int unsigned     default 0                 not null comment '排序',
    stock       int unsigned     default 0                 not null comment '库存',
    cur_stock   int unsigned     default 0                 not null comment '库存',
    used_stock  int unsigned     default 0                 not null comment '库存',
    over_stock  int unsigned     default 0                 not null comment '库存',
    lock_stock  int unsigned     default 0                 not null comment '库存'
)
    comment 'Box Goods Table';

create index idx_active
    on blind_box.box_goods (active_id);

create index idx_level
    on blind_box.box_goods (level_id, level_name);

create index idx_spu_sku
    on blind_box.box_goods (spu_id, sku_id);

create table blind_box.box_goods_stock_log
(
    id         bigint unsigned auto_increment comment 'Primary Key'
        primary key,
    is_deleted tinyint unsigned default 0                 not null comment 'Soft Delete',
    created_at timestamp        default CURRENT_TIMESTAMP not null comment 'Creation Timestamp',
    updated_at timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment 'Update Timestamp',
    user_id    bigint unsigned  default 0                 not null comment 'User ID',
    goods_id   bigint unsigned  default 0                 not null comment '商品id',
    trade_id   bigint unsigned  default 0                 not null comment '交易id',
    change_num int              default 0                 not null comment '变更数量'
)
    comment 'Box Goods Stock Log Table';

create index idx_goods
    on blind_box.box_goods_stock_log (goods_id);

create index idx_trade
    on blind_box.box_goods_stock_log (trade_id);

create index idx_user
    on blind_box.box_goods_stock_log (user_id);

create table blind_box.box_level
(
    id         int auto_increment
        primary key,
    level_name varchar(20)                                   not null,
    updated_at timestamp           default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP,
    created_at timestamp           default CURRENT_TIMESTAMP not null,
    is_deleted tinyint(4) unsigned default 0                 not null
)
    comment '盲盒款式定义' charset = utf8;

create table blind_box.box_order_delivery
(
    id              bigint unsigned auto_increment comment '主键 ID'
        primary key,
    created_at      timestamp       default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at      timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted      tinyint(1)      default 0                 not null comment '软删标记,0-正常,1-已删除',
    order_id        bigint unsigned                           not null comment '订单ID，与 box_order_info.id 关联',
    express_company varchar(50)                               null comment '快递公司',
    express_code    varchar(20)                               null comment '快递公司编码（如：SF=顺丰）',
    tracking_number varchar(100)    default ''                not null comment '快递单号',
    delivery_status int unsigned    default 1                 not null comment '发货状态,1-待发货,2-已发货,3-已送达,4-已退回',
    shipped_at      bigint          default 0                 null comment '发货时间戳',
    delivered_at    bigint          default 0                 null comment '送达时间戳',
    remark          varchar(500)                              null comment '备注信息',
    admin_id        bigint unsigned default 0                 null comment '操作管理员ID（AccountID）',
    constraint uk_order_id
        unique (order_id)
)
    comment '箱子订单物流信息';

create table blind_box.box_order_info
(
    id              bigint auto_increment
        primary key,
    user_id         bigint              default 0                 not null,
    out_trade_no    varchar(30)         default ''                not null comment '订单号',
    purchase_type   tinyint             default 0                 not null comment '1 盲盒 3 购买',
    transaction_id  varchar(40)         default ''                not null comment '支付订单号',
    fee_type        varchar(10)         default 'CNY'             not null comment '货币类型, CNY',
    total_fee       bigint(10)          default 0                 not null comment '总金额',
    fee             bigint(10)          default 0                 not null comment '单价',
    active_id       bigint              default 0                 not null comment '活动ID',
    box_id          bigint              default 0                 not null comment '箱子ID',
    box_no          varchar(255)        default ''                not null comment '箱子编号',
    box_slot        varchar(255)        default ''                not null,
    lottery_total   int(10)             default 0                 not null comment '总可抽奖次数',
    lottery_left    int(10)             default 0                 not null comment '剩余可抽奖次数',
    order_status    tinyint(1)          default 0                 not null comment '支付状态,0-订单生成,1-订单创建失败,2-支付中,3-支付成功,4-支付失败,5-已退款',
    created_at      timestamp           default CURRENT_TIMESTAMP null,
    updated_at      timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    source_platform tinyint(1)          default 0                 not null comment '来源平台(用户UID的注册平台),1:微信',
    pay_method      tinyint(1)          default 0                 not null comment '支付方式,1:微信 2:余额,3:微信+魔币,4:余额+魔币 5-支付宝 6-魔魂',
    consume_type    tinyint(1)                                    null comment '消费类型,1:活动;2:快递;3:退款;4-试玩 5-交易 6-余额提现',
    consume_msg     varchar(100)        default ''                not null comment '消费信息',
    order_type      tinyint(1)                                    null comment '订单类型',
    remark          varchar(100)                                  null,
    pay_time        bigint              default 0                 not null,
    used_fee        bigint(10)          default 0                 not null comment '余额',
    cash_fee        bigint              default 0                 not null comment '实际支付金额',
    freight_fee     bigint              default 0                 not null comment '运费金额',
    points_fee      bigint              default 0                 not null comment '积分抵扣金额',
    points_use      bigint              default 0                 not null comment '积分抵扣数额',
    coupon_fee      int                 default 0                 not null comment '优惠券抵扣金额',
    coupon_id       int                 default 0                 not null comment '优惠券id',
    is_deleted      tinyint(4) unsigned default 0                 not null,
    delivery_id     tinyint             default 0                 not null comment '配送方式',
    shop_id         int                 default 0                 not null comment '店铺地址',
    address_id      int                 default 0                 not null comment '用户地址',
    pay_expire_time bigint              default 0                 null comment '最后支付时间，秒级时间戳',
    merchant_id     int                 default 0                 not null,
    pos_order_no    varchar(64)         default ''                not null
)
    comment '盲盒订单信息表' charset = utf8;

create index i_active_id_and_account_id
    on blind_box.box_order_info (active_id);

create index idx_created_time
    on blind_box.box_order_info (created_at);

create index index_out_trade_no
    on blind_box.box_order_info (out_trade_no);

create table blind_box.box_queue_lock
(
    id                int auto_increment
        primary key,
    user_id           bigint                                        null,
    active_id         bigint                                        null,
    box_id            bigint                                        null,
    box_no            varchar(20)                                   null,
    invalid_show_time datetime                                      null comment '前端显示的过期时间',
    invalid_time      timestamp                                     null comment '过期时间',
    lock_time         timestamp                                     null,
    remark            varchar(2000)                                 null,
    created_at        timestamp           default CURRENT_TIMESTAMP not null,
    updated_at        timestamp           default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP,
    is_deleted        tinyint(4) unsigned default 0                 not null,
    lock_type         int                                           null comment '1 箱子排队'
)
    comment '盲盒排队锁表' charset = utf8;

create table blind_box.box_record_info
(
    id            bigint auto_increment
        primary key,
    created_at    datetime            default CURRENT_TIMESTAMP not null,
    updated_at    datetime            default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    is_deleted    tinyint(4) unsigned default 0                 not null,
    active_id     int                                           not null comment '活动id',
    box_no        varchar(50)                                   not null comment '盒子编号',
    cap           int                                           not null comment '盒子容量',
    len           int                                           not null comment '盒子余量',
    ava_slot      varchar(100)                                  null comment '可用槽位',
    sold_slot     varchar(100)                                  null comment '已用槽位',
    pre_lock_slot varchar(100)                                  null comment '预锁槽位',
    lock_slot     varchar(100)                                  null comment '锁定槽位',
    user_id       int                 default 0                 not null
)
    comment '盲盒盒子记录表' charset = utf8;

create index idx_user
    on blind_box.box_record_info (user_id);

create table blind_box.box_trade_detail_info
(
    id              bigint auto_increment
        primary key,
    order_id        bigint              default 0                 not null comment '支付订单表ID',
    active_id       bigint              default 0                 not null comment '活动ID',
    box_id          bigint              default 0                 not null comment '箱子ID',
    box_no          varchar(255)        default '0'               null comment '箱子编号',
    box_slot        varchar(500)        default ''                not null,
    goods_id        bigint              default 0                 not null comment '赏品ID',
    spu_id          bigint              default 0                 not null comment '赏品ID',
    sku_id          bigint              default 0                 not null comment '赏品ID',
    batch_id        bigint              default 0                 not null comment '批次ID',
    quantity        int                 default 0                 not null comment '数量',
    goods_level     varchar(10)         default ''                not null comment '赏品级别',
    user_id         bigint              default 0                 not null comment '购买者ID',
    goods_name      varchar(300)        default ''                not null comment '赏品名称',
    active_title    varchar(30)         default ''                not null comment '活动标题',
    trade_status    tinyint(1)          default 0                 not null comment '1 已支付',
    delivery_status tinyint(1)          default 0                 not null comment '发货状态 0未发货  1申请中  2出库中 3已发货',
    created_at      timestamp           default CURRENT_TIMESTAMP not null,
    updated_at      timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    remark          varchar(100)                                  null,
    is_deleted      tinyint(4) unsigned default 0                 not null,
    trans_status    tinyint(1)          default 1                 not null comment '交易状态 1-等待交易 2-已转移  3-发布中 4-已成交 5-已取消',
    active_type     tinyint(1)          default 0                 not null comment '活动类型（1-抽赏活动  2-盲盒活动 3-团战 4-乱斗 5-排行 6-夺宝 7 魔魂团战 8-兑换 9-打擂 10-扭蛋 11-试玩 12-DC券发放 13-无限赏''）'
)
    comment '交易详细表' charset = utf8;

create index idx_active_id
    on blind_box.box_trade_detail_info (active_id);

create index idx_box_id
    on blind_box.box_trade_detail_info (box_id);

create index idx_created_time
    on blind_box.box_trade_detail_info (created_at);

create index idx_goods_id
    on blind_box.box_trade_detail_info (goods_id);

create index idx_order_id
    on blind_box.box_trade_detail_info (order_id);

create index idx_user_id
    on blind_box.box_trade_detail_info (user_id, trans_status);

create index idx_user_id_active_id
    on blind_box.box_trade_detail_info (user_id, active_id);

create table blind_box.card_day_send_log
(
    id         int auto_increment
        primary key,
    user_id    int                 default 0                 not null,
    day        date                                          null,
    is_deleted tinyint(4) unsigned default 0                 not null,
    created_at timestamp           default CURRENT_TIMESTAMP not null,
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '道具免费发放记录' charset = utf8;

create index user_id_index
    on blind_box.card_day_send_log (user_id);

create table blind_box.config_admin
(
    id           int(11) unsigned auto_increment
        primary key,
    config_type  tinyint             default 0                 not null comment '1 系统配置 2 页面配置',
    name         varchar(32)         default ''                not null,
    config_key   varchar(32)         default ''                not null comment '唯一key',
    config_value longtext                                      null comment '配置值',
    `desc`       varchar(64)         default ''                not null comment '描述',
    status       tinyint(3)          default 1                 not null comment '状态 1启用 2禁用',
    is_deleted   tinyint(4) unsigned default 0                 not null comment '软删',
    created_at   timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at   timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint idx_config_key
        unique (config_key)
)
    comment '配置表';

create table blind_box.coupon
(
    id               bigint unsigned auto_increment
        primary key,
    code             varchar(128)     default ''                not null comment '优惠券code',
    name             varchar(128)     default ''                not null comment '优惠券名称',
    discount_type    tinyint unsigned default 0                 not null comment '折扣类型 1整单折扣',
    discount_method  tinyint unsigned default 0                 not null comment '优惠方式 1现金立减',
    discount_amount  int(11) unsigned default 0                 not null comment '优惠金额(分)',
    threshold_type   tinyint unsigned default 0                 not null comment '门槛类型 0无门槛 1实付满减',
    threshold_amount int(11) unsigned default 0                 not null comment '门槛金额',
    expire_days      int(11) unsigned default 0                 not null comment '领取有效天数',
    range_type       tinyint unsigned default 0                 not null comment '使用范围 0全场无限制 1指定活动id',
    extra            varchar(255)     default ''                not null comment '文字展示配置',
    status           tinyint unsigned default 1                 not null comment '状态 1启用 2禁用',
    is_deleted       tinyint unsigned default 0                 not null,
    created_at       timestamp        default CURRENT_TIMESTAMP not null,
    updated_at       timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '优惠券表' charset = utf8;

create index idx_code
    on blind_box.coupon (code);

create table blind_box.coupon_issue
(
    id         bigint unsigned auto_increment
        primary key,
    name       varchar(64)      default ''                not null comment '发放名称',
    issue_type tinyint unsigned default 1                 not null comment '发放类型: 1全部用户 2指定用户',
    start_time int(11) unsigned default 0                 not null comment '开始时间',
    end_time   int(11) unsigned default 0                 not null comment '结束时间',
    coupon_id  bigint unsigned  default 0                 not null comment '优惠券id',
    source_id  int(11) unsigned                           not null comment '优惠券来源id',
    stock      int(11) unsigned default 0                 not null comment '库存(发放数量)',
    status     tinyint unsigned default 0                 not null comment '状态 1启用 2停用',
    is_deleted tinyint unsigned default 0                 not null,
    created_at timestamp        default CURRENT_TIMESTAMP not null,
    updated_at timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '优惠券发放表' charset = utf8;

create index idx_cid
    on blind_box.coupon_issue (coupon_id);

create index idx_sid_status
    on blind_box.coupon_issue (source_id, status);

create index idx_type_status
    on blind_box.coupon_issue (issue_type, status);

create table blind_box.coupon_range
(
    coupon_id   bigint unsigned     default 0 not null comment '优惠券id',
    entity_type tinyint(4) unsigned default 0 not null comment '实体类型',
    entity_id   bigint unsigned     default 0 not null comment '实体id',
    primary key (coupon_id, entity_type, entity_id)
)
    comment '优惠券使用范围表' charset = utf8;

create index idx_cid_et
    on blind_box.coupon_range (coupon_id, entity_type);

create index idx_et_eid
    on blind_box.coupon_range (entity_type, entity_id);

create table blind_box.coupon_source
(
    id         bigint unsigned auto_increment
        primary key,
    name       varchar(64)      default ''                not null comment '来源名称',
    sort       tinyint unsigned default 0                 not null comment '排序',
    is_deleted tinyint unsigned default 0                 not null,
    created_at timestamp        default CURRENT_TIMESTAMP not null,
    updated_at timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '优惠券来源表' charset = utf8;

create table blind_box.coupon_target_user
(
    issue_id bigint unsigned default 0 not null comment '发放id',
    user_id  bigint unsigned default 0 not null comment '用户id',
    primary key (issue_id, user_id)
)
    comment '优惠券定向用户表' charset = utf8;

create index idx_issue
    on blind_box.coupon_target_user (issue_id);

create index idx_uid
    on blind_box.coupon_target_user (user_id);

create table blind_box.coupon_user
(
    id              bigint unsigned auto_increment
        primary key,
    user_id         bigint unsigned  default 0                 not null comment '用户id',
    coupon_id       bigint unsigned  default 1                 not null comment '优惠券id',
    issue_id        bigint unsigned  default 0                 not null comment '优惠券发放id',
    source_id       tinyint unsigned default 0                 not null comment '优惠券来源id',
    trade_no        varchar(128)     default ''                not null comment '支付流水号',
    discount_amount int(11) unsigned default 0                 not null comment '订单优惠金额',
    expire_time     int(11) unsigned default 0                 not null comment '过期时间',
    use_time        int(11) unsigned default 0                 not null comment '使用时间',
    status          tinyint unsigned default 0                 not null comment '状态: 1 已领取 2使用中 3已使用 4已作废',
    remark          varchar(255)     default ''                not null comment '备注',
    is_deleted      tinyint unsigned default 0                 not null,
    created_at      timestamp        default CURRENT_TIMESTAMP not null,
    updated_at      timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '用户优惠券表' charset = utf8;

create index idx_iid_status
    on blind_box.coupon_user (issue_id, status);

create index idx_uid_iid
    on blind_box.coupon_user (user_id, issue_id);

create index idx_uid_sid
    on blind_box.coupon_user (user_id, source_id);

create index idx_uid_status
    on blind_box.coupon_user (user_id, status);

create table blind_box.goods_batch
(
    id         bigint auto_increment
        primary key,
    title      varchar(64)  default ''                not null comment '标题',
    sell_type  tinyint      default 0                 not null comment '1现货 2预售',
    status     tinyint      default 1                 not null comment '启用禁用',
    `desc`     varchar(255) default ''                not null comment '描述',
    is_deleted tinyint(1)   default 0                 not null comment '是否删除，0未删除，1已删除',
    created_at datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '修改时间',
    constraint unique_name_idx
        unique (title, `desc`, is_deleted)
)
    comment '商品批次表';

create table blind_box.goods_sku
(
    id             bigint auto_increment comment 'SKU ID'
        primary key,
    spu_id         bigint                                 not null comment 'SPU ID',
    batch_id       bigint       default 0                 not null comment '批次id',
    title          varchar(255) default ''                not null comment '标题',
    code           varchar(64)  default ''                not null comment 'SKU 编号',
    cover          varchar(1000)                          not null comment '商品的图片',
    total          int          default 0                 not null comment '库存',
    lock_num       int          default 0                 not null comment '锁定数',
    used_num       int          default 0                 not null comment '销量',
    refund_num     int          default 0                 not null comment '退货数',
    original_price bigint                                 not null comment '原价',
    sell_price     bigint                                 not null comment '销售价',
    ref_price      bigint                                 not null comment '成本价',
    bar_code       varchar(100) default ''                not null comment '条形码',
    delivery_list  varchar(255) default ''                not null comment '递送方式',
    status         tinyint(3)   default 0                 not null comment '状态 1上架 2下架',
    is_deleted     tinyint(1)   default 0                 not null comment '是否删除，0未删除，1已删除',
    created_at     datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at     datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '修改时间'
)
    comment '商品SKU表';

create index idx_code
    on blind_box.goods_sku (code);

create index idx_spu
    on blind_box.goods_sku (spu_id);

create index idx_title
    on blind_box.goods_sku (title);

create table blind_box.goods_sku_shop
(
    spu_id  bigint default 0 not null comment 'spu id',
    sku_id  bigint default 0 not null comment 'sku id',
    shop_id bigint default 0 not null comment 'shop id',
    constraint uid_sku_shop
        unique (sku_id, shop_id)
)
    comment 'sku批次关联表';

create index idx_shop
    on blind_box.goods_sku_shop (shop_id);

create index idx_spu
    on blind_box.goods_sku_shop (spu_id);

create table blind_box.goods_sku_stock_log
(
    id         bigint auto_increment comment 'ID'
        primary key,
    spu_id     bigint                               not null comment 'spu id',
    sku_id     bigint     default 0                 not null comment 'sku id',
    user_id    bigint     default 0                 not null comment '用户',
    log_type   tinyint    default 0                 not null comment 'log类型',
    sub_type   tinyint    default 0                 not null comment '具体变化类型',
    entity_id  bigint     default 0                 not null comment '关联id',
    value      int(20)    default 0                 not null comment '操作值',
    before_val int(20)    default 0                 not null comment '变化前数值',
    after_val  int(20)    default 0                 not null comment '变化后数值',
    create_by  bigint     default 0                 not null comment '操作人',
    is_deleted tinyint(1) default 0                 not null comment '是否删除，0未删除，1已删除',
    created_at datetime   default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at datetime   default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '修改时间'
);

create index idx_sku
    on blind_box.goods_sku_stock_log (sku_id);

create table blind_box.goods_sku_unit
(
    id         bigint auto_increment comment 'id'
        primary key,
    title      varchar(64) default ''                not null comment '名称',
    spu_id     bigint      default 0                 not null comment 'SPU ID',
    sku_id     bigint      default 0                 not null comment 'SKU ID',
    unit_ratio int         default 1                 not null comment '单位比',
    is_primary tinyint     default 0                 not null comment '是否主规格',
    status     tinyint     default 0                 not null comment '状态',
    is_deleted tinyint     default 0                 not null comment '是否删除，0未删除，1已删除',
    created_at datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '修改时间'
)
    comment '商品SKU单位表';

create table blind_box.goods_spu
(
    id             bigint auto_increment comment 'SPU ID'
        primary key,
    batch_id       bigint       default 0                 not null comment '批次id',
    title          varchar(255)                           not null comment '标题',
    sub_title      varchar(255) default ''                not null comment '子标题',
    code           varchar(64)  default ''                not null comment 'SPU编号',
    cover          varchar(128) default ''                not null comment '封面图',
    brand_id       bigint       default 0                 not null comment '品牌ID',
    vendor_id      bigint       default 0                 not null comment '厂商ID',
    supplier_id    bigint       default 0                 not null comment '供应商ID',
    status         tinyint(1)   default 0                 not null comment '状态 1上架 2下架',
    pre_start_time int          default 0                 not null comment '预售开始时间',
    pre_end_time   int          default 0                 not null comment '预售结束时间',
    lowest_price   bigint                                 not null comment '最低价格',
    highest_price  bigint                                 not null comment '最高价格',
    is_redeem      tinyint      default 0                 not null comment '1积分兑换',
    sort           int          default 0                 not null comment '排序值',
    detail         text                                   null comment '商品描述',
    is_deleted     tinyint(1)   default 0                 not null comment '是否删除，0未删除，1已删除',
    created_at     datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at     datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '修改时间'
)
    comment '商品SPU表，SPU和SKU是一对多关系';

create index idx_brand
    on blind_box.goods_spu (brand_id, vendor_id, supplier_id);

create index idx_code
    on blind_box.goods_spu (code);

create index idx_created_at
    on blind_box.goods_spu (created_at);

create index idx_price
    on blind_box.goods_spu (lowest_price, id);

create index idx_saleable
    on blind_box.goods_spu (status);

create index idx_title
    on blind_box.goods_spu (title, sub_title);

create table blind_box.goods_spu_batch
(
    spu_id     bigint  default 0 not null comment 'spu id',
    batch_id   bigint  default 0 not null comment 'batch id',
    is_default tinyint default 0 not null comment '1默认',
    constraint uid_spu_batch
        unique (spu_id, batch_id)
)
    comment 'sku批次关联表';

create index idx_batch
    on blind_box.goods_spu_batch (batch_id);

create index idx_spu
    on blind_box.goods_spu_batch (spu_id);

create table blind_box.goods_spu_tag
(
    spu_id   bigint default 0 not null comment 'SPU ID',
    group_id bigint default 0 not null comment '标签组ID',
    tag_id   bigint default 0 not null comment '标签ID',
    constraint udx_spu_group_tag
        unique (spu_id, group_id, tag_id)
)
    comment 'SPU标签关联表，SPU和标签是多对多关系';

create table blind_box.goods_tag
(
    id         bigint auto_increment
        primary key,
    name       varchar(64) collate utf8mb4_bin default ''                not null,
    group_id   tinyint                         default 0                 not null comment '分组id',
    status     tinyint                         default 1                 not null comment '启用禁用',
    is_deleted tinyint(1)                      default 0                 not null comment '是否删除，0未删除，1已删除',
    created_at datetime                        default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at datetime                        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '修改时间',
    constraint unique_name_idx
        unique (name, group_id, is_deleted)
)
    comment '标签表';

create table blind_box.item_card_config
(
    id          int auto_increment
        primary key,
    card_status tinyint             default 1                 not null comment '卡状态，默认启用为1，0为禁用',
    card_code   varchar(50)         default ''                not null comment '卡码',
    card_type   tinyint(1)          default 0                 not null comment '道具卡类型` 1 摇一摇 2 提示卡 3 透视卡',
    card_desc   varchar(50)         default ''                not null comment '道具卡后台名称',
    card_name   varchar(50)         default ''                not null comment '道具卡名称',
    card_img    varchar(200)        default ''                not null comment '道具图片',
    detail_msg  varchar(50)         default ''                not null comment '详情说明',
    expire_day  int(10)                                       null comment '过期天数',
    is_deleted  tinyint(4) unsigned default 0                 not null comment '是否有效，0是 1否',
    created_at  timestamp           default CURRENT_TIMESTAMP not null,
    updated_at  timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    delete_time timestamp                                     null,
    remark      varchar(255)                                  null,
    source      int                 default 0                 not null comment '来源',
    constraint vision_card_config_pk
        unique (card_code)
)
    comment '道具卡配置表' charset = utf8;

create table blind_box.market_banner
(
    id         bigint auto_increment
        primary key,
    title      varchar(1999)       default ''                not null comment '广告标题',
    position   int(10)             default 0                 not null comment '1 首页',
    created_at timestamp           default CURRENT_TIMESTAMP not null,
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    image      varchar(1999)                                 null comment '图片',
    jump_url   varchar(1999)       default '2'               null comment '跳转链接',
    jump_type  tinyint(1)                                    not null comment ' 0-不跳转 1-活动 2-h5 3-小程序原生 4- app原生 5-单品',
    status     tinyint(1)          default 0                 not null,
    sort       int(20)                                       not null comment '排序',
    active_id  bigint                                        null,
    channel    tinyint(4) unsigned                           null comment '渠道',
    jump_param varchar(255)                                  null,
    is_deleted tinyint(4) unsigned default 0                 not null comment '是否删除'
)
    comment 'banner' charset = utf8;

create table blind_box.openapi_merchant
(
    id                  bigint unsigned auto_increment comment '主键ID'
        primary key,
    created_at          datetime   default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at          datetime   default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    is_deleted          tinyint(1) default 0                 not null comment '软删除：0-未删除，1-已删除',
    app_id              varchar(64)                          not null comment '应用ID（AppKey）',
    app_secret          varchar(128)                         not null comment '应用密钥',
    merchant_name       varchar(128)                         not null comment '商户名称',
    merchant_code       varchar(64)                          not null comment '商户编码',
    contact_name        varchar(64)                          null comment '联系人姓名',
    contact_mobile      varchar(20)                          null comment '联系人手机',
    contact_email       varchar(128)                         null comment '联系人邮箱',
    status              tinyint(1) default 1                 not null comment '状态：1-启用，2-禁用',
    callback_url        varchar(512)                         null comment '默认回调地址',
    ip_whitelist        text                                 null comment 'IP白名单，多个用逗号分隔，支持CIDR',
    daily_limit         bigint     default 100000            not null comment '每日调用限制',
    qps_limit           int        default 100               not null comment 'QPS限制',
    single_amount_limit bigint     default 10000000          not null comment '单笔金额限制（分）',
    daily_amount_limit  bigint     default 100000000         not null comment '每日金额限制（分）',
    allowed_skus        text                                 null comment '允许的SKU列表，JSON格式',
    remark              varchar(512)                         null comment '备注',
    constraint uk_app_id
        unique (app_id),
    constraint uk_merchant_code
        unique (merchant_code)
)
    comment 'OpenAPI商户配置表';

create index idx_created_at
    on blind_box.openapi_merchant (created_at);

create index idx_status
    on blind_box.openapi_merchant (status);

create table blind_box.openapi_merchant_log
(
    id            bigint unsigned auto_increment comment '主键ID'
        primary key,
    merchant_id   bigint                             not null comment '商户ID',
    app_id        varchar(64)                        not null comment '应用ID',
    api_path      varchar(128)                       not null comment 'API路径',
    request_time  datetime                           not null comment '请求时间',
    request_ip    varchar(45)                        not null comment '请求IP',
    request_body  text                               null comment '请求内容',
    response_code int                                not null comment '响应码',
    response_body text                               null comment '响应内容',
    response_time int                                not null comment '响应时间（毫秒）',
    signature     varchar(128)                       null comment '请求签名',
    nonce         varchar(64)                        null comment 'Nonce值',
    error_msg     varchar(512)                       null comment '错误信息',
    created_at    datetime default CURRENT_TIMESTAMP not null comment '创建时间'
)
    comment 'OpenAPI商户访问日志表';

create index idx_app_id
    on blind_box.openapi_merchant_log (app_id);

create index idx_merchant_id
    on blind_box.openapi_merchant_log (merchant_id);

create index idx_request_ip
    on blind_box.openapi_merchant_log (request_ip);

create index idx_request_time
    on blind_box.openapi_merchant_log (request_time);

create table blind_box.openapi_merchant_stats
(
    id                bigint unsigned auto_increment comment '主键ID'
        primary key,
    merchant_id       bigint                             not null comment '商户ID',
    stat_date         date                               not null comment '统计日期',
    total_requests    bigint   default 0                 not null comment '总请求数',
    success_requests  bigint   default 0                 not null comment '成功请求数',
    failed_requests   bigint   default 0                 not null comment '失败请求数',
    total_amount      bigint   default 0                 not null comment '总交易金额（分）',
    max_qps           int      default 0                 not null comment '最大QPS',
    avg_response_time int      default 0                 not null comment '平均响应时间（毫秒）',
    created_at        datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at        datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint uk_merchant_date
        unique (merchant_id, stat_date)
)
    comment 'OpenAPI商户统计表';

create index idx_stat_date
    on blind_box.openapi_merchant_stats (stat_date);

create table blind_box.resource_brand
(
    id         int(11) unsigned auto_increment
        primary key,
    name       varchar(32)         default ''                not null comment '公司名称',
    cover      varchar(128)        default ''                not null comment '封面',
    `desc`     varchar(255)                                  null,
    sort       int(11) unsigned    default 0                 not null comment '排序值',
    status     tinyint(4) unsigned default 1                 not null comment '状态 1启用 2禁用',
    is_deleted tinyint             default 0                 not null comment '软删',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '品牌表';

create table blind_box.resource_image
(
    id           int(11) unsigned auto_increment
        primary key,
    mapping_type tinyint      default 1                 not null comment '1活动 2spu',
    mapping_id   bigint       default 0                 not null,
    url          varchar(128) default ''                not null comment '封面',
    is_deleted   tinyint      default 0                 not null comment '软删',
    created_at   timestamp    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at   timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '品牌表';

create table blind_box.resource_shop
(
    id          int(11) unsigned auto_increment
        primary key,
    name        varchar(32)         default ''                not null comment '店铺名称',
    cover       varchar(128)        default ''                not null comment '封面',
    address     varchar(128)        default ''                not null comment '地址',
    mobile      varchar(64)         default ''                not null comment '联系方式',
    sort        int(11) unsigned    default 0                 not null comment '排序值',
    status      tinyint(4) unsigned default 1                 not null comment '状态 1启用 2禁用',
    description varchar(255)        default ''                not null comment '描述',
    is_deleted  tinyint             default 0                 not null comment '软删',
    created_at  timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at  timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '店铺表';

create table blind_box.resource_supplier
(
    id         int(11) unsigned auto_increment
        primary key,
    name       varchar(32)         default ''                not null comment '公司名称',
    cover      varchar(128)        default ''                not null comment '封面',
    sort       int(11) unsigned    default 0                 not null comment '排序值',
    status     tinyint(4) unsigned default 1                 not null comment '状态 1启用 2禁用',
    is_deleted tinyint             default 0                 not null comment '软删',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '供应商表';

create table blind_box.resource_vendor
(
    id         int(11) unsigned auto_increment
        primary key,
    name       varchar(32)         default ''                not null comment '厂商名称',
    cover      varchar(128)        default ''                not null comment '封面',
    `desc`     varchar(255)                                  null,
    sort       int(11) unsigned    default 0                 not null comment '排序值',
    status     tinyint(4) unsigned default 1                 not null comment '状态 1启用 2禁用',
    is_deleted tinyint             default 0                 not null comment '软删',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '厂商表';

create table blind_box.user
(
    id              bigint unsigned auto_increment
        primary key,
    nickname        varchar(64)         default ''                not null comment '用户名称',
    country_code    varchar(32)         default ''                not null comment '区号',
    mobile          varchar(32)         default ''                not null comment '手机号',
    email           varchar(64)         default ''                not null comment '邮箱',
    avatar          varchar(128)        default ''                not null comment '头像',
    status          tinyint(4) unsigned default 1                 not null comment '状态 1启用 2禁用',
    platform        int                 default 0                 not null comment '平台',
    tide_val        int                 default 0                 not null comment '潮气值',
    points          int                 default 0                 not null comment '积分',
    last_login_time int                 default 0                 not null comment '最近登录时间',
    is_deleted      tinyint(4) unsigned default 0                 not null comment '软删',
    created_at      timestamp           default CURRENT_TIMESTAMP not null,
    updated_at      timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '用户表';

create table blind_box.user_address
(
    id         bigint unsigned auto_increment
        primary key,
    user_id    bigint unsigned     default 0                 not null comment '用户id',
    consignee  varchar(32)         default ''                not null comment '收货人',
    mobile     varchar(32)         default ''                not null comment '联系电话',
    area       varchar(128)        default ''                not null comment '地区',
    address    varchar(128)        default ''                not null comment '具体地址',
    is_default tinyint(4) unsigned default 0                 not null comment '是否为默认地址',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '用户地址表';

create index idx_uid
    on blind_box.user_address (user_id);

create table blind_box.user_address_backup
(
    id          bigint unsigned auto_increment
        primary key,
    user_id     bigint unsigned     default 0                 not null comment '用户id',
    consignee   varchar(32)         default ''                not null comment '收货人',
    mobile      varchar(32)         default ''                not null comment '联系电话',
    province_id int(11) unsigned    default 0                 not null comment '省份id',
    city_id     int(11) unsigned    default 0                 not null comment '城市id',
    district_id int(11) unsigned    default 0                 not null comment '地区id',
    address     varchar(128)        default ''                not null comment '具体地址',
    is_default  tinyint(4) unsigned default 0                 not null comment '是否为默认地址',
    is_deleted  tinyint(4) unsigned default 0                 not null comment '软删',
    created_at  timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at  timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '用户地址表';

create index idx_uid
    on blind_box.user_address_backup (user_id);

create table blind_box.user_auth
(
    id         bigint unsigned auto_increment
        primary key,
    user_id    bigint                                 not null comment '用户id',
    auth_type  tinyint(3)   default 0                 not null comment '登陆类型 1微信小程序',
    auth_uid   varchar(128) default ''                not null comment '第三方唯一标识符',
    auth_pid   varchar(128) default ''                not null,
    created_at timestamp    default CURRENT_TIMESTAMP not null,
    updated_at timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    constraint idx_at_uid
        unique (auth_type, auth_uid)
)
    comment '用户认证表';

create index idx_uid
    on blind_box.user_auth (user_id);

create table blind_box.user_cart
(
    id          bigint auto_increment
        primary key,
    user_id     bigint unsigned  default 0                 not null comment '用户id',
    spu_id      bigint unsigned  default 0                 not null comment 'spu id',
    sku_id      bigint unsigned  default 0                 not null comment 'sku id',
    unit_num    int(11) unsigned default 0                 not null comment '数量',
    delivery_id int              default 0                 not null comment 'delivery id',
    shop_id     bigint           default 0                 not null comment 'shop id',
    is_deleted  tinyint          default 0                 not null comment '软删',
    created_at  timestamp        default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at  timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '用户购物车表';

create index idx_uid_sku
    on blind_box.user_cart (user_id, sku_id);

create table blind_box.user_collect
(
    id          bigint unsigned auto_increment
        primary key,
    user_id     bigint unsigned default 0                 not null comment '用户id',
    entity_type tinyint         default 0                 not null comment 'entity type',
    entity_id   bigint unsigned default 0                 not null comment 'entity id',
    is_deleted  tinyint         default 0                 not null comment '软删',
    created_at  timestamp       default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at  timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint udx_uid_eid
        unique (user_id, entity_id)
)
    comment '用户收藏表';

create table blind_box.user_item_card_info
(
    id          int auto_increment
        primary key,
    user_id     bigint       default 0                 not null comment '用户id',
    card_code   varchar(64)  default ''                not null comment '卡码',
    card_type   tinyint(1)   default 0                 not null comment '道具卡类型` 1 单抽折扣',
    card_name   varchar(64)  default ''                not null comment '道具卡名称',
    card_img    varchar(255) default ''                not null comment '道具图片',
    detail_msg  varchar(128) default ''                not null comment '详情说明',
    status      tinyint(1)   default 1                 not null comment '状态  1未使用 2锁定 3已使用 4已过期',
    used_time   int          default 0                 not null comment '使用时间',
    active_id   int          default 0                 not null comment '使用的活动id',
    box_id      int          default 0                 not null comment '箱子id',
    box_slot    int          default 0                 not null comment '箱子槽位',
    expire_time int          default 0                 not null comment '过期时间',
    source_type int          default 0                 not null comment '来源渠道 1后台发放',
    create_by   bigint       default 0                 not null,
    is_deleted  tinyint(1)   default 0                 not null comment '是否有效 0有效 1已删除',
    remark      varchar(255) default ''                not null,
    created_at  timestamp    default CURRENT_TIMESTAMP not null,
    updated_at  timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '用户道具卡信息表' charset = utf8;

create index expire_time_index
    on blind_box.user_item_card_info (expire_time);

create index idx_coupon_code
    on blind_box.user_item_card_info (card_code);

create index idx_user_id
    on blind_box.user_item_card_info (user_id);

create table blind_box.user_level
(
    id         int(11) unsigned auto_increment
        primary key,
    name       varchar(32)         default ''                not null comment '名称',
    icon       varchar(128)        default ''                not null comment 'icon',
    min        int                 default 0                 not null comment '最小值',
    max        int                 default 0                 not null comment '最大值',
    sort       int(11) unsigned    default 0                 not null comment '排序值',
    status     tinyint(4) unsigned default 1                 not null comment '状态 1启用 2禁用',
    is_deleted tinyint             default 0                 not null comment '软删',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '用户等级表';

create table blind_box.user_log
(
    id         bigint unsigned auto_increment
        primary key,
    user_id    bigint unsigned default 0                 not null comment '用户id',
    method     varchar(32)     default ''                not null comment '请求方式',
    trace_id   varchar(32)     default ''                not null comment '请求id',
    url        varchar(64)     default ''                not null comment '请求url',
    client_ip  varchar(32)     default ''                not null comment '客户端ip',
    header     text                                      null comment '请求头',
    body       text                                      null comment '请求体',
    created_at timestamp       default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '用户操作日志表';

create table blind_box.user_login_log
(
    id          bigint unsigned auto_increment
        primary key,
    user_id     bigint unsigned default 0                 not null comment '用户id',
    client_ip   varchar(32)     default ''                not null comment '登录ip',
    client_type tinyint         default 0                 not null comment '客户端类型',
    device      varchar(255)    default ''                not null comment '登录设备',
    user_agent  varchar(255)    default ''                not null comment 'user agent',
    header      text                                      not null comment 'header头',
    created_at  timestamp       default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at  timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '用户登录log表';

create index idx_uid
    on blind_box.user_login_log (user_id);

create table blind_box.user_points_log
(
    id               bigint unsigned auto_increment comment '主键ID'
        primary key,
    user_id          bigint unsigned  default 0                 not null comment '用户ID',
    action_type      tinyint unsigned default 0                 not null comment '操作类型: 1增加(earn) 2减少(spend) 3抵扣明细(offset) 4过期扣减(expired)',
    points           bigint unsigned  default 0                 not null comment '变更积分数',
    remaining_points bigint unsigned                            null comment '剩余积分数（仅对earn类型有效，初始值=points）',
    reference_log_id bigint unsigned                            null comment '关联日志ID（offset类型指向对应的earn记录）',
    balance_before   bigint unsigned  default 0                 not null comment '操作前余额',
    balance_after    bigint unsigned  default 0                 not null comment '操作后余额',
    source_type      tinyint unsigned default 0                 not null comment '来源类型',
    source_id        bigint unsigned  default 0                 not null comment '来源ID（如订单ID）',
    remark           varchar(255)     default ''                not null comment '备注',
    expire_at        timestamp                                  null comment '积分过期时间（NULL表示永不过期，仅对earn类型有效）',
    is_expired       tinyint unsigned default 0                 not null comment '是否已过期 0-未过期 1-已过期',
    is_deleted       tinyint unsigned default 0                 not null comment '软删除 0-未删除 1-已删除',
    created_at       timestamp        default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at       timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '修改时间'
)
    comment '用户积分日志表' collate = utf8mb4_unicode_ci;

create index idx_user_created
    on blind_box.user_points_log (user_id, created_at, is_deleted);

create table blind_box.user_reward_log
(
    id         bigint unsigned auto_increment
        primary key,
    user_id    bigint unsigned default 0                 not null comment '用户id',
    reward_id  bigint          default 0                 not null comment '奖励id',
    created_at timestamp       default CURRENT_TIMESTAMP not null,
    updated_at timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '用户领取奖励记录表' charset = utf8;

create index idx_uid_rid
    on blind_box.user_reward_log (user_id, reward_id);

create table blind_box.user_sign_config
(
    id          bigint unsigned auto_increment
        primary key,
    days        tinyint unsigned default 0                 not null comment '连续签到天数(1-7)',
    reward_type tinyint unsigned default 1                 not null comment '奖励类型：1 潮气值',
    reward_val  int(11) unsigned default 0                 not null comment '奖励值',
    sort        tinyint unsigned default 0                 not null comment '排序',
    is_deleted  tinyint unsigned default 0                 not null,
    created_at  timestamp        default CURRENT_TIMESTAMP not null,
    updated_at  timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '用户签到配置表' charset = utf8;

create table blind_box.user_sign_log
(
    id                 bigint unsigned auto_increment
        primary key,
    user_id            bigint unsigned  default 0                 not null,
    sign_date          date                                       not null comment '签到日期',
    continue_sign_days tinyint unsigned default 0                 not null comment '连续签到天数',
    is_deleted         tinyint unsigned default 0                 not null comment '是否删除',
    created_at         timestamp        default CURRENT_TIMESTAMP not null,
    updated_at         timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '用户签到表' charset = utf8;

create index idx_uid
    on blind_box.user_sign_log (user_id);

create table blind_box.user_subscribe
(
    id                 bigint unsigned auto_increment
        primary key,
    user_id            bigint unsigned  default 0                 not null comment '用户id',
    entity_id          bigint unsigned  default 0                 not null comment '实体id',
    entity_type        tinyint          default 0                 not null comment '实体类型',
    entity_relation_id bigint           default 0                 not null comment '实体关联id',
    status             tinyint unsigned default 0                 not null comment '1待通知 2已通知 3无效',
    is_deleted         tinyint unsigned default 0                 not null comment '软删',
    created_at         timestamp        default CURRENT_TIMESTAMP not null,
    updated_at         timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    constraint udx_uid_eid_type
        unique (user_id, entity_id, entity_type)
)
    comment '用户订阅表' charset = utf8;

create index idx_uid
    on blind_box.user_subscribe (user_id);

create table blind_box.user_tide_card_config
(
    id                bigint unsigned auto_increment
        primary key,
    card_code         varchar(128)     default ''                not null comment '道具卡code',
    card_type         tinyint unsigned default 1                 not null comment '道具卡类型：1 透视卡 2提示卡',
    card_num          tinyint unsigned default 0                 not null comment '卡片数量',
    original_tide_val int(11) unsigned default 0                 not null comment '原来所需潮气值',
    tide_val          int(11) unsigned default 0                 not null comment '所需潮气值',
    expire_hour       tinyint unsigned default 0                 not null comment '兑换后过期时间(小时为单位)',
    day_limit         tinyint unsigned default 1                 not null comment '每日兑换次数',
    sort              tinyint unsigned default 0                 not null comment '排序',
    is_deleted        tinyint unsigned default 0                 not null,
    created_at        timestamp        default CURRENT_TIMESTAMP not null,
    updated_at        timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '用户潮气值可兑换道具配置表' charset = utf8;

create table blind_box.user_tide_card_exchange_log
(
    id            bigint unsigned auto_increment
        primary key,
    user_id       bigint unsigned  default 0                 not null,
    tc_config_id  bigint unsigned  default 1                 not null comment '潮气值可兑换道具配置表id',
    exchange_date date                                       not null comment '兑换日期',
    remark        varchar(256)     default ''                not null comment '备注:如道具卡标识json',
    is_deleted    tinyint unsigned default 0                 not null,
    created_at    timestamp        default CURRENT_TIMESTAMP not null,
    updated_at    timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '用户潮气值兑换道具记录表' charset = utf8;

create index idx_uid
    on blind_box.user_tide_card_exchange_log (user_id);

create table blind_box.user_tide_log
(
    id            bigint unsigned auto_increment
        primary key,
    user_id       bigint unsigned  default 0                 not null,
    tide_val_type tinyint unsigned default 1                 not null comment '潮气值类型：1增加 2减少',
    tide_val      int(11) unsigned default 0                 not null comment '潮气值',
    source        tinyint unsigned default 1                 not null comment '潮气值来源: 1注册奖励 2每日签到 3每日任务 4每日消退 5道具兑换 6系统发放or扣除
',
    source_id     bigint unsigned  default 0                 not null comment '来源id',
    remark        varchar(256)     default ''                not null comment '备注',
    is_deleted    tinyint unsigned default 0                 not null,
    created_at    timestamp        default CURRENT_TIMESTAMP not null,
    updated_at    timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '用户潮气值log表' charset = utf8;

create index idx_uid
    on blind_box.user_tide_log (user_id);

create table blind_box.user_tide_task_config
(
    id             bigint unsigned auto_increment
        primary key,
    title          varchar(128)     default ''                not null comment '标题',
    cover          varchar(128)     default ''                not null comment '封面图',
    task_type      tinyint unsigned default 1                 not null comment '任务类型:1 摇一摇 2抽盒',
    accomplish_num tinyint unsigned default 1                 not null comment '任务完成次数',
    tide_val       int(11) unsigned default 0                 not null comment '奖励的潮气值',
    sort           tinyint unsigned default 0                 not null comment '排序',
    is_deleted     tinyint unsigned default 0                 not null,
    created_at     timestamp        default CURRENT_TIMESTAMP not null,
    updated_at     timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '用户潮气值每日任务配置表' charset = utf8;

create table blind_box.user_wxpay_bill
(
    id             bigint unsigned auto_increment
        primary key,
    bill_no        varchar(32)         default ''                not null comment '明细号',
    user_id        bigint unsigned     default 0                 not null comment '用户id',
    action         tinyint             default 0                 not null comment '1支付 2退款',
    pay_type       tinyint(4) unsigned default 0                 not null comment '1全款 2定金 3尾款',
    wx_type        tinyint             default 0                 not null comment '1小程序',
    order_id       bigint unsigned     default 0                 not null comment '订单id',
    order_goods_id bigint              default 0                 not null comment '订单商品id',
    amount         bigint unsigned     default 0                 not null comment '金额(分)',
    valid_amount   bigint              default 0                 not null comment '有效金额(分)',
    status         tinyint(3)          default 0                 not null comment '状态 1成功 2失败',
    create_by      bigint              default 0                 not null comment '创建人',
    remark         varchar(255)        default ''                not null comment '备注',
    is_deleted     tinyint(4) unsigned default 0                 not null comment '软删',
    created_at     timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at     timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '微信支付账单表';

create index idx_etype
    on blind_box.user_wxpay_bill (pay_type);

create index idx_no_uid
    on blind_box.user_wxpay_bill (bill_no, user_id);

create index idx_oid_ogid
    on blind_box.user_wxpay_bill (order_id, order_goods_id);

create table blind_box.wxpay_callback_log
(
    id               bigint unsigned auto_increment comment '日志ID'
        primary key,
    notify_id        varchar(64)      default ''                not null comment '微信通知id',
    req_id           varchar(64)      default ''                not null comment '请求id',
    entity_type      tinyint          default 0                 not null comment '实体类型1支付 2退款',
    wx_entity_id     varchar(64)      default ''                not null comment '微信侧单号',
    out_entity_no    varchar(64)      default ''                not null comment '业务侧单号',
    notify_status    varchar(64)      default ''                not null comment '通知状态',
    notify_data      text                                       null comment '通知内容',
    resource_decrypt text                                       null comment '解密内容',
    status           tinyint unsigned default 0                 not null comment '状态',
    created_at       timestamp        default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at       timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '微信回调日志表';

create table blind_box.wxpay_log
(
    id              bigint unsigned auto_increment comment '日志ID'
        primary key,
    user_id         bigint unsigned default 0                 not null comment '用户id',
    req_id          varchar(64)     default ''                not null comment '请求id',
    amount          bigint          default 0                 not null comment '金额',
    entity_type     tinyint         default 0                 not null comment '实体类型1支付 2退款',
    request_func    varchar(64)     default ''                not null comment '请求方法',
    wx_entity_id    varchar(64)     default ''                not null comment '微信支付or退款id',
    out_entity_no   varchar(64)     default ''                not null comment '商户支付or退款单号',
    status          tinyint         default 0                 not null comment '状态',
    out_entity_info text                                      null comment 'bill信息',
    request_data    text                                      null comment '请求头信息',
    response_data   text                                      null comment '响应内容',
    created_at      timestamp       default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at      timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '微信支付日志表';

create table blind_box.wxpay_refund_log
(
    id                bigint unsigned auto_increment comment '主键'
        primary key,
    user_id           bigint unsigned                            null comment '用户ID',
    order_id          bigint unsigned                            null comment '订单ID',
    pay_no            varchar(64)                                null comment '原支付订单号',
    out_refund_no     varchar(64)                                not null comment '商户退款单号（唯一）',
    wx_refund_id      varchar(64)                                null comment '微信退款单号',
    wx_transaction_id varchar(64)                                null comment '微信支付订单号',
    total_amount      bigint unsigned                            null comment '订单总金额(分)',
    refund_amount     bigint unsigned                            null comment '退款金额(分)',
    refund_status     int unsigned                               null comment '退款状态：1退款中 2成功 3失败 4已取消',
    refund_reason     int unsigned                               null comment '退款原因：1用户申请 2管理员退款 3系统错误 4订单取消 5商品问题',
    refund_desc       varchar(255)                               null comment '退款说明',
    success_time      timestamp                                  null comment '退款成功时间',
    fail_reason       varchar(255)                               null comment '失败原因',
    notify_url        varchar(255)                               null comment '退款通知地址',
    request_data      text                                       null comment '请求数据(JSON)',
    response_data     text                                       null comment '响应数据(JSON)',
    admin_id          bigint unsigned                            null comment '操作管理员ID',
    remark            varchar(255)                               null comment '备注',
    is_deleted        tinyint unsigned default 0                 null comment '软删除标记：0正常 1删除',
    created_at        timestamp        default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at        timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '修改时间',
    constraint uk_out_refund_no
        unique (out_refund_no)
)
    comment '微信退款日志表';

create index idx_order_id
    on blind_box.wxpay_refund_log (order_id);

create index idx_pay_no
    on blind_box.wxpay_refund_log (pay_no);

create index idx_user_id
    on blind_box.wxpay_refund_log (user_id);

