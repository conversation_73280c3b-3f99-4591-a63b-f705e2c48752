package config

type BoxConfigReq struct {
}

type BoxConfigResp struct {
	YaoNum             uint32   `json:"yaoNum"`             // 摇号数量
	PostFee            uint64   `json:"postFee"`            // 邮费
	PostThreshold      uint64   `json:"postThreshold"`      // 邮费门槛
	PostCnt            uint32   `json:"postCnt"`            // 邮费数量
	Announcement       string   `json:"announcement"`       // 公告
	UnderConstruct     bool     `json:"underConstruct"`     // 是否维护中
	UnderConstructTime []uint32 `json:"underConstructTime"` // 维护时间
	TotalFee           uint64   `json:"totalFee"`           // 总费用
}
