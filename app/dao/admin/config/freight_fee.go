package config

import (
	"encoding/json"
)

// FreightFeeRule 快递费规则配置
type FreightFeeRule struct {
	Enable      bool   `json:"enable"`      // 是否启用
	Threshold   uint64 `json:"threshold"`   // 门槛金额（分）
	Fee         uint64 `json:"fee"`         // 快递费（分）
	Description string `json:"description"` // 描述
}

// GetFreightFeeRule 获取快递费规则配置
func (m *Model) GetFreightFeeRule() (*FreightFeeRule, error) {
	if m.ConfigKey != ConfigKeyFreightFeeRule {
		return nil, nil
	}

	var rule FreightFeeRule
	if err := json.Unmarshal([]byte(m.ConfigValue), &rule); err != nil {
		return nil, err
	}

	return &rule, nil
}

// SetFreightFeeRule 设置快递费规则配置
func (m *Model) SetFreightFeeRule(rule *FreightFeeRule) error {
	ruleBytes, err := json.Marshal(rule)
	if err != nil {
		return err
	}

	m.ConfigKey = ConfigKeyFreightFeeRule
	m.ConfigValue = string(ruleBytes)

	return nil
}

// GetDefaultFreightFeeRule 获取默认快递费规则：99元以下收8元运费
func GetDefaultFreightFeeRule() *FreightFeeRule {
	return &FreightFeeRule{
		Enable:      true, // 默认启用
		Threshold:   9900, // 99元 = 9900分
		Fee:         800,  // 8元 = 800分
		Description: "订单满99元免运费，不满99元收取8元运费",
	}
}

// CalculateFreightFee 根据配置计算运费
// totalAmount: 商品总金额（未计算各种优惠）
func CalculateFreightFee(totalAmount uint64, rule *FreightFeeRule) uint64 {
	if rule == nil || !rule.Enable {
		return 0
	}

	// 如果总金额小于门槛，需要收取运费
	if totalAmount < rule.Threshold {
		return rule.Fee
	}

	// 满足门槛金额，免运费
	return 0
}
