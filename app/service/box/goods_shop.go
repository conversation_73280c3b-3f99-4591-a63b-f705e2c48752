package box

import (
	"fmt"

	actDao "blind_box/app/dao/activity"
	configDao "blind_box/app/dao/admin/config"
	"blind_box/app/dao/box/box_goods"
	skuDao "blind_box/app/dao/goods/sku"
	boxDto "blind_box/app/dto/box"
	"blind_box/app/dto/goods"
	"blind_box/app/service/common"
	"blind_box/app/service/coupon"
	"blind_box/app/service/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"
	"blind_box/pkg/util/decimalUtil"

	"blind_box/app/common/dbs"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// 商品列表接口
func (e *Entry) GoodsList(ctx *gin.Context, req *boxDto.GoodsListReq) (res *boxDto.GoodsListRes, err error) {
	res = &boxDto.GoodsListRes{}
	res.List = make([]*boxDto.GoodsListItem, 0)

	var (
		goodsList  []*box_goods.Model
		goodsCount int64
		activeMap  = make(map[uint64]*actDao.Model)
		eg         errgroup.Group
	)

	// 构建查询条件
	filter := &box_goods.Filter{
		LevelID: req.LevelID,
	}
	if req.ActiveID > 0 {
		filter.ActiveID = req.ActiveID
	}

	// 查询商品列表和总数
	goodsCount, goodsList, err = e.BoxGoodsRepo.DataPageList(ctx, filter, req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("GoodsList DataPageList")
		return nil, err
	}

	res.Count = goodsCount
	if goodsCount <= 0 {
		res.IsEnd = true
		return res, nil
	}

	if len(goodsList) < req.Limit {
		res.IsEnd = true
	}

	// 获取关联数据
	activeIDs := make([]uint64, 0)

	// 获取activeIDs
	for _, goods := range goodsList {
		activeIDs = append(activeIDs, goods.ActiveID)
	}

	if len(activeIDs) > 0 {
		eg.Go(func() error {
			activeList, err := e.ActiveRepo.FindByFilter(ctx, &actDao.Filter{
				Ids: activeIDs,
			})
			if err != nil {
				return err
			}
			activeMap = activeList.GetIDMap()
			return nil
		})
	}

	if err = eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("GoodsList errgroup.Wait")
		return nil, err
	}

	// 构建响应数据
	for _, goods := range goodsList {
		item := &boxDto.GoodsListItem{
			ID:         goods.ID,
			GoodsName:  goods.GoodsName,
			GoodsCover: goods.GoodsCover,
			LevelName:  goods.LevelName,
			Stock:      goods.Stock,
			CurStock:   goods.GetCurStock(),
		}

		// 获取活动信息作为商品价格
		if active, ok := activeMap[goods.ActiveID]; ok {
			item.Price = active.ActPrice
			item.OriginPrice = active.ActPrice // 暂时设为相同，后续可扩展折扣逻辑
		}

		// 计算销售率
		if goods.Stock > 0 {
			item.SaleRate = float64(goods.Stock-goods.GetCurStock()) / float64(goods.Stock) * 100
		}

		res.List = append(res.List, item)
	}

	return res, nil
}

// 商品详情接口
func (e *Entry) GoodsDetail(ctx *gin.Context, req *boxDto.GoodsDetailReq) (res *boxDto.GoodsDetailRes, err error) {
	res = &boxDto.GoodsDetailRes{}

	var (
		goodsModel  *box_goods.Model
		activeModel *actDao.Model
		skuMap      map[uint64]goods.CrSkuItem
		eg          errgroup.Group
	)

	// 获取商品信息
	goodsModel, err = e.BoxGoodsRepo.FetchByID(ctx, req.GoodsID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("GoodsDetail FetchByID")
		return nil, err
	}
	if goodsModel == nil {
		return nil, ecode.ParamErr
	}

	// 并行获取关联数据
	eg.Go(func() error {
		activeModel, err = e.ActiveRepo.FetchByID(ctx, goodsModel.ActiveID)
		return err
	})

	eg.Go(func() error {
		if goodsModel.SpuID > 0 || goodsModel.SkuID > 0 {
			skuMap, err = common.GetService().GetCommonSkuResp(ctx, []uint64{goodsModel.SpuID}, []uint64{goodsModel.SkuID})
		}
		return err
	})

	if err = eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("GoodsDetail errgroup.Wait")
		return nil, err
	}

	// 构建基础信息
	res.ID = goodsModel.ID
	res.GoodsName = goodsModel.GoodsName
	res.GoodsCover = goodsModel.GoodsCover
	res.LevelName = goodsModel.LevelName
	res.Stock = goodsModel.Stock
	res.CurStock = goodsModel.GetCurStock()
	res.Description = goodsModel.GoodsName // 暂时使用商品名作为描述

	// 设置价格信息
	if activeModel != nil {
		res.Price = activeModel.ActPrice
		res.OriginPrice = activeModel.ActPrice

		// 活动信息
		res.ActiveInfo = &boxDto.GoodsActiveInfo{
			ActiveID:    activeModel.ID,
			ActiveTitle: activeModel.Title,
			ActiveType:  activeModel.ActType,
			StartTime:   activeModel.GetStartTime(),
			EndTime:     activeModel.GetEndTime(),
		}
	}

	// SKU信息
	if goodsModel.SkuID > 0 {
		res.SkuInfo = &boxDto.GoodsSkuInfo{
			SpuID: goodsModel.SpuID,
			SkuID: goodsModel.SkuID,
		}

		// 从SKU详情中获取更多信息
		if len(skuMap) > 0 {
			// 这里需要根据实际的SKU数据结构来解析
			// 暂时先预留结构
		}
	}

	// 统计信息
	res.Statistics = &boxDto.GoodsStatistics{
		TotalSales: goodsModel.Stock - goodsModel.GetCurStock(),
	}
	if goodsModel.Stock > 0 {
		res.Statistics.SaleRate = float64(goodsModel.Stock-goodsModel.GetCurStock()) / float64(goodsModel.Stock) * 100
	}

	return res, nil
}

// 直接购买接口（统一购物车模式）
func (e *Entry) DirectPurchase(ctx *gin.Context, req *boxDto.DirectPurchaseReq) (res interface{}, err error) {
	res = &boxDto.DirectPurchaseRes{}

	if req.OrderID > 0 {
		payResult, err := e.DirectPay(ctx, &boxDto.BoxPayReq{
			OrderID: req.OrderID,
		})
		if err != nil {
			return nil, err
		}
		return payResult, nil
	}

	// 验证购物车条目（统一使用CartItems，无论单个商品还是多商品）
	cartItems := req.CartItems
	skuMap := make(map[uint64]bool)

	for i, item := range cartItems {
		if item.SkuID == 0 {
			return nil, fmt.Errorf("cart item %d: SkuID is required", i+1)
		}
		if item.Quantity == 0 {
			return nil, fmt.Errorf("cart item %d: quantity must be greater than 0", i+1)
		}

		// 检查SKU是否重复
		if skuMap[item.SkuID] {
			return nil, fmt.Errorf("duplicate SKU: %d in cart", item.SkuID)
		}
		skuMap[item.SkuID] = true
	}

	// 统计信息
	var totalItems uint32
	for _, item := range cartItems {
		totalItems += item.Quantity
	}

	// 构建BoxPay请求
	purchaseType := STRATEGY_DIRECT

	boxPayReq := &boxDto.BoxPayReq{
		UserID:       req.UserID,
		PayMethod:    req.PayMethod,
		CouponID:     req.CouponID,
		PurchaseType: uint32(purchaseType),
		CartItems:    req.CartItems,

		DeliveryID: req.DeliveryID,
		ShopID:     req.ShopID,
		AddressID:  req.AddressID,
	}

	payResult, err := e.DirectPay(ctx, boxPayReq)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("DirectPurchase BoxPay")
		return nil, err
	}

	return payResult, nil
}

// 商品库存检查接口（支持CartItems）
func (e *Entry) GoodsStockCheck(ctx *gin.Context, req *boxDto.GoodsStockCheckReq) (res *boxDto.GoodsStockCheckRes, err error) {
	res = &boxDto.GoodsStockCheckRes{
		Items:        make([]*boxDto.CartItemStockInfo, 0, len(req.CartItems)),
		SkuCount:     uint32(len(req.CartItems)),
		AllAvailable: true,
	}

	// 验证购物车条目
	skuMap := make(map[uint64]bool)
	skuIDs := make([]uint64, 0, len(req.CartItems))

	for i, item := range req.CartItems {
		if item.SkuID == 0 {
			return nil, fmt.Errorf("cart item %d: SkuID is required", i+1)
		}
		if item.Quantity == 0 {
			return nil, fmt.Errorf("cart item %d: quantity must be greater than 0", i+1)
		}

		// 检查SKU是否重复
		if skuMap[item.SkuID] {
			return nil, fmt.Errorf("duplicate SKU: %d in cart", item.SkuID)
		}
		skuMap[item.SkuID] = true
		skuIDs = append(skuIDs, item.SkuID)
	}

	// 批量查询SKU信息 - 优化N+1查询问题
	skuModels := make(map[uint64]*skuDao.Model)

	// 使用批量查询替代逐个查询
	skuList, err := e.SkuRepo.FindByFilter(ctx, &skuDao.Filter{
		Ids: skuIDs,
	})
	if err != nil {
		// 保持原有容错逻辑：查询失败时为所有SKU创建错误信息，但继续处理
		log.Ctx(ctx).WithError(err).Error("GoodsStockCheck FindByFilter batch query failed")
		for _, skuID := range skuIDs {
			errorItem := &boxDto.CartItemStockInfo{
				SkuID:        skuID,
				SkuName:      "Unknown SKU",
				Quantity:     0,
				Available:    false,
				IsInStock:    false,
				CanOrder:     false,
				ErrorMessage: fmt.Sprintf("SKU %d query failed", skuID),
			}
			res.Items = append(res.Items, errorItem)
			res.AllAvailable = false
		}
	} else {
		// 将查询结果转换为map
		for _, skuModel := range skuList {
			skuModels[skuModel.ID] = skuModel
		}

		// 检查是否有SKU未找到，为未找到的SKU创建错误信息
		for _, skuID := range skuIDs {
			if _, exists := skuModels[skuID]; !exists {
				// SKU不存在
				errorItem := &boxDto.CartItemStockInfo{
					SkuID:        skuID,
					SkuName:      "Unknown SKU",
					Quantity:     0,
					Available:    false,
					IsInStock:    false,
					CanOrder:     false,
					ErrorMessage: fmt.Sprintf("SKU %d not found", skuID),
				}
				res.Items = append(res.Items, errorItem)
				res.AllAvailable = false
			}
		}
	}

	// 构建库存检查结果
	for _, item := range req.CartItems {
		skuModel, exists := skuModels[item.SkuID]
		if !exists {
			// 这个SKU在上面的循环中已经处理过了
			continue
		}

		// 检查SKU状态是否可订购
		canOrder := skuModel.JudgeCanOrdered()
		availableStock := skuModel.GetUsableNum()
		//totalStock := skuModel.Total

		// 判断是否有库存
		isInStock := availableStock > 0

		// 判断是否有足够的库存满足需求数量
		available := availableStock >= item.Quantity

		// 构建错误信息
		errorMessage := ""
		if !canOrder {
			errorMessage = fmt.Sprintf("商品 %s 当前不可订购", skuModel.Title)
		} else if !isInStock {
			errorMessage = fmt.Sprintf("商品 %s 已售罄", skuModel.Title)
		} else if !available {
			errorMessage = fmt.Sprintf("商品 %s 库存不足", skuModel.Title)
		}

		// 构建库存信息
		stockInfo := &boxDto.CartItemStockInfo{
			SkuID:    item.SkuID,
			SkuName:  skuModel.Title,
			SkuCover: helper.GetImageCdnUrl(ctx, skuModel.Cover),
			Quantity: item.Quantity,
			//AvailableStock: availableStock,
			//TotalStock:     totalStock,
			Available:    available,
			IsInStock:    isInStock,
			CanOrder:     canOrder,
			ErrorMessage: errorMessage,
		}
		res.Items = append(res.Items, stockInfo)

		// 累计商品数量
		res.TotalItems += item.Quantity

		// 如果任何一个SKU库存不足，则整体不可用
		if !available || !canOrder {
			res.AllAvailable = false
		}
	}

	log.Ctx(ctx).Info("GoodsStockCheck: SkuCount=%d, TotalItems=%d, AllAvailable=%v",
		res.SkuCount, res.TotalItems, res.AllAvailable)

	return res, nil
}

// 商品价格计算接口（统一CartItems模式）
func (e *Entry) GoodsPriceCalc(ctx *gin.Context, req *boxDto.GoodsPriceCalcReq) (res *boxDto.GoodsPriceCalcRes, err error) {
	res = &boxDto.GoodsPriceCalcRes{
		Items:    make([]*boxDto.CartItemPriceInfo, 0, len(req.CartItems)),
		SkuCount: uint32(len(req.CartItems)),
	}

	// 验证购物车条目
	skuMap := make(map[uint64]bool)
	skuIDs := make([]uint64, 0, len(req.CartItems))

	for i, item := range req.CartItems {
		if item.SkuID == 0 {
			return nil, fmt.Errorf("cart item %d: SkuID is required", i+1)
		}
		if item.Quantity == 0 {
			return nil, fmt.Errorf("cart item %d: quantity must be greater than 0", i+1)
		}

		// 检查SKU是否重复
		if skuMap[item.SkuID] {
			return nil, fmt.Errorf("duplicate SKU: %d in cart", item.SkuID)
		}
		skuMap[item.SkuID] = true
		skuIDs = append(skuIDs, item.SkuID)
	}

	// 批量查询SKU信息 - 优化N+1查询问题
	skuModels := make(map[uint64]*skuDao.Model)

	// 使用批量查询替代逐个查询
	skuList, err := e.SkuRepo.FindByFilter(ctx, &skuDao.Filter{
		Ids: skuIDs,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("GoodsPriceCalc FindByFilter batch query failed")
		return nil, fmt.Errorf("SKU batch query failed: %w", err)
	}

	// 将查询结果转换为map
	for _, skuModel := range skuList {
		skuModels[skuModel.ID] = skuModel
	}

	// 检查是否有SKU未找到
	for _, skuID := range skuIDs {
		if _, exists := skuModels[skuID]; !exists {
			return nil, fmt.Errorf("SKU %d not found", skuID)
		}
	}

	// 使用价格计算器计算总价
	calculator := decimalUtil.NewPriceCalculator()
	cartItems := make([]decimalUtil.CartItem, 0, len(req.CartItems))

	// 构建价格明细并准备计算器输入
	for _, item := range req.CartItems {
		skuModel := skuModels[item.SkuID]

		// 计算单个商品的总价
		itemTotalPrice, err := decimalUtil.SafeMultiply(skuModel.SellPrice, item.Quantity)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("GoodsPriceCalc item price calculation overflow")
			return nil, fmt.Errorf("SKU %d quantity too large: %w", item.SkuID, err)
		}

		// 检查库存和可订购状态
		canOrder := skuModel.JudgeCanOrdered()
		availableStock := skuModel.GetUsableNum()
		available := availableStock >= item.Quantity

		// 构建商品明细
		itemInfo := &boxDto.CartItemPriceInfo{
			SkuID:      item.SkuID,
			SkuName:    skuModel.Title,
			SkuCover:   helper.GetImageCdnUrl(ctx, skuModel.Cover),
			UnitPrice:  skuModel.SellPrice,
			Quantity:   item.Quantity,
			TotalPrice: itemTotalPrice,
			Available:  available,
			CanOrder:   canOrder,
		}
		res.Items = append(res.Items, itemInfo)

		// 累计商品数量
		res.TotalItems += item.Quantity

		// 为价格计算器准备数据
		cartItems = append(cartItems, decimalUtil.CartItem{
			SkuID:     item.SkuID,
			UnitPrice: skuModel.SellPrice,
			Quantity:  item.Quantity,
		})
	}

	// 使用价格计算器计算总价
	priceResult, err := calculator.CalculateCartTotal(cartItems)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("GoodsPriceCalc CalculateCartTotal")
		return nil, fmt.Errorf("price calculation failed: %w", err)
	}

	res.TotalFee = priceResult.SubTotal
	res.UsedFee = priceResult.FinalAmount

	// 计算运费
	// 获取运费配置
	freightFeeRule, err := e.getFreightFeeRule(ctx)
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("GoodsPriceCalc get freight fee rule failed")
		// 获取配置失败时，不影响订单流程，运费设为0
		res.FreightFee = 0
	} else {
		// 根据商品总价（未计算优惠）计算运费
		res.FreightFee = configDao.CalculateFreightFee(res.TotalFee, freightFeeRule)
		if res.FreightFee > 0 {
			// 将运费加入到最终金额中
			newUsedFee, err := decimalUtil.SafeAdd(res.UsedFee, res.FreightFee)
			if err == nil {
				res.UsedFee = newUsedFee
			}
		}
	}

	// 处理优惠券（如果有）
	if req.CouponID > 0 && req.UserID > 0 {
		couponSrv := coupon.GetService()
		// 注意：这里可能需要根据实际业务逻辑调整优惠券验证方法
		// 目前使用总金额进行验证，如果有更复杂的规则可以后续调整
		couponInfo, err := couponSrv.CheckWhenUseDirectPurchaseCoupon(
			ctx,
			req.UserID,
			req.CouponID,
			res.TotalFee,
			0) // 购物车模式，不传具体SKU ID

		if err != nil {
			log.Ctx(ctx).WithError(err).Warn("GoodsPriceCalc coupon validation failed, skip coupon discount")
		} else if couponInfo != nil {
			// 直接计算优惠券折扣
			couponAmount := couponInfo.DiscountAmount

			// 确保优惠券折扣不超过总价
			if couponAmount > res.TotalFee {
				couponAmount = res.TotalFee
			}

			// 使用安全减法计算最终价格
			finalAmount, err := decimalUtil.SafeSubtract(res.TotalFee, couponAmount)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("GoodsPriceCalc final amount calculation error")
			} else {
				res.CouponFee = couponAmount
				res.UsedFee = finalAmount
				res.TotalSaved = couponAmount

				log.Ctx(ctx).Info("GoodsPriceCalc applied coupon: CouponID=%d, CouponFee=%d, UsedFee=%d",
					req.CouponID, res.CouponFee, res.UsedFee)
			}
		}
	}

	// 处理积分抵扣（如果有）
	if req.UsePoints == 1 && req.UserID > 0 {
		userSrv := user.GetService()

		// 计算积分抵扣
		pointsReq := &user.PointsDiscountRequest{
			UserID:      req.UserID,
			TotalAmount: res.UsedFee, // 使用优惠券后的金额作为基准
			MaxPercent:  95,          // 最大抵扣95%，确保至少支付5%
		}

		pointsResp, err := userSrv.CalculatePointsDiscount(ctx, pointsReq)
		if err != nil {
			log.Ctx(ctx).WithError(err).Warn("GoodsPriceCalc points discount calculation failed, skip points discount")
		} else if pointsResp != nil && pointsResp.DiscountAmount > 0 {
			// 确保积分抵扣后至少还有0.01元需要支付
			minPaymentAmount := uint64(1) // 0.01元 = 1分
			maxPointsDiscount := res.UsedFee
			if res.UsedFee > minPaymentAmount {
				maxPointsDiscount = res.UsedFee - minPaymentAmount
			} else {
				maxPointsDiscount = 0 // 如果订单金额小于0.01元，不允许积分抵扣
			}

			actualPointsDiscount := pointsResp.DiscountAmount
			if actualPointsDiscount > maxPointsDiscount {
				actualPointsDiscount = maxPointsDiscount
			}

			if actualPointsDiscount > 0 {
				// 使用安全减法计算积分抵扣后的金额
				finalAmount, err := decimalUtil.SafeSubtract(res.UsedFee, actualPointsDiscount)
				if err != nil {
					log.Ctx(ctx).WithError(err).Error("GoodsPriceCalc points discount calculation error")
				} else {
					res.PointsFee = actualPointsDiscount
					res.PointsUse = pointsResp.CanUsePoints // 消耗的积分数额
					res.UsedFee = finalAmount

					// 累加总节省金额
					newTotalSaved, err := decimalUtil.SafeAdd(res.TotalSaved, actualPointsDiscount)
					if err == nil {
						res.TotalSaved = newTotalSaved
					}

					log.Ctx(ctx).Info("GoodsPriceCalc applied points discount: UserID=%d, PointsFee=%d, PointsUse=%d, UsedFee=%d",
						req.UserID, res.PointsFee, res.PointsUse, res.UsedFee)
				}
			}
		}
	}

	log.Ctx(ctx).Info("GoodsPriceCalc: UserID=%d, SkuCount=%d, TotalItems=%d, TotalFee=%d, FreightFee=%d, CouponFee=%d, PointsFee=%d, PointsUse=%d, UsedFee=%d",
		req.UserID, res.SkuCount, res.TotalItems, res.TotalFee, res.FreightFee, res.CouponFee, res.PointsFee, res.PointsUse, res.UsedFee)

	return res, nil
}

// getFreightFeeRule 获取运费配置规则
func (e *Entry) getFreightFeeRule(ctx *gin.Context) (*configDao.FreightFeeRule, error) {
	// 优先尝试从 KeyPostFee 配置获取固定运费值
	postFee := configDao.RedisGetConfig[uint64](ctx, configDao.KeyPostFee)
	if postFee > 0 {
		// 如果配置了固定运费值，创建一个简单的运费规则
		// Threshold 设为0表示无论订单金额多少都收取运费
		return &configDao.FreightFeeRule{
			Enable:      true,
			Threshold:   0,
			Fee:         postFee,
			Description: "固定运费配置",
		}, nil
	}

	// 如果没有 KeyPostFee 配置或值为0，回退到原有的 FreightFeeRule 逻辑
	configModel, err := e.ConfigRepo.FindByFilter(ctx, &configDao.Filter{
		ConfigKey: configDao.ConfigKeyFreightFeeRule,
		Status:    uint32(dbs.StatusEnable),
	})
	if err != nil {
		return nil, err
	}
	if configModel == nil {
		// 如果没有配置，使用默认配置
		return configDao.GetDefaultFreightFeeRule(), nil
	}

	// 解析配置
	if len(configModel) == 0 {
		return configDao.GetDefaultFreightFeeRule(), nil
	}
	freightFeeRule, err := configModel[0].GetFreightFeeRule()
	if err != nil {
		return nil, err
	}
	if freightFeeRule == nil {
		return configDao.GetDefaultFreightFeeRule(), nil
	}

	return freightFeeRule, nil
}
