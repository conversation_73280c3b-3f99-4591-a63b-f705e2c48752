package box

import (
	"sync"

	activeDao "blind_box/app/dao/activity"
	configDao "blind_box/app/dao/admin/config"
	"blind_box/app/dao/box/box_action_detail"
	"blind_box/app/dao/box/box_active_config"
	"blind_box/app/dao/box/box_goods"
	"blind_box/app/dao/box/box_goods_stock_log"
	"blind_box/app/dao/box/box_level"
	"blind_box/app/dao/box/box_must_config"
	"blind_box/app/dao/box/box_queue_lock"
	"blind_box/app/dao/box/box_record"
	"blind_box/app/dao/card/card_config"
	"blind_box/app/dao/card/card_day_send_log"
	"blind_box/app/dao/card/card_user"
	"blind_box/app/dao/coupon"
	coupon_user "blind_box/app/dao/coupon/coupon_user"
	"blind_box/app/dao/goods/sku"
	"blind_box/app/dao/goods/spu"
	orderDao "blind_box/app/dao/order/order"
	orderDetailDao "blind_box/app/dao/order/order_detail"
	imageDao "blind_box/app/dao/resource/image"
	"blind_box/app/dao/user"
	payLog "blind_box/app/dao/wechat/pay_log"
	boxDto "blind_box/app/dto/box"
	common "blind_box/app/service/common"
	userService "blind_box/app/service/user"
	"blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Server interface {
	BoxCore

	AdminBoxActiveConfig

	AdminBoxLevel

	AdminBoxMustConfig

	BoxQueueLock
}

type BoxCore interface {
	payBox(ctx *gin.Context, tx *gorm.DB, req *BoxSelectReq) (res *BoxSelectRes, err error)
}

type AdminBoxActiveConfig interface {
	AdminActiveConfigCreate(ctx *gin.Context, req *boxDto.AdminActiveConfigCreateReq) (res *boxDto.AdminActiveConfigCreateResp, err error)

	AdminActiveConfigUpdate(ctx *gin.Context, req *boxDto.AdminActiveConfigUpdateReq) (res *boxDto.AdminActiveConfigUpdateResp, err error)

	AdminActiveConfigInfo(ctx *gin.Context, req *boxDto.AdminActiveConfigInfoReq) (res *boxDto.AdminActiveConfigInfoResp, err error)
}

type AdminBoxMustConfig interface {
	AdminBoxMustConfigBagCreate(ctx *gin.Context, req *boxDto.AdminBoxMustConfigBagCreateReq) (res *boxDto.AdminBoxMustConfigBagCreateResp, err error)

	AdminBoxMustConfigUserCreate(ctx *gin.Context, req *boxDto.AdminBoxMustConfigUserCreateReq) (res *boxDto.AdminBoxMustConfigUserCreateResp, err error)

	AdminBoxMustConfigActiveCreate(ctx *gin.Context, req *boxDto.AdminBoxMustConfigActiveCreateReq) (res *boxDto.AdminBoxMustConfigActiveCreateResp, err error)

	AdminBoxMustConfigInfo(ctx *gin.Context, req *boxDto.AdminBoxMustConfigInfoReq) (res *boxDto.AdminBoxMustConfigInfoResp, err error)

	AdminBoxMustConfigActiveDel(ctx *gin.Context, req *boxDto.AdminBoxMustConfigActiveDelReq) (res *boxDto.AdminBoxMustConfigActiveDelResp, err error)
}

type AdminBoxLevel interface {
	AdminBoxLevelAdd(ctx *gin.Context, req *boxDto.AdminBoxLevelAddReq) (res *boxDto.AdminBoxLevelAddResp, err error)

	AdminBoxLevelUpdate(ctx *gin.Context, req *boxDto.AdminBoxLevelUpdateReq) (res *boxDto.AdminBoxLevelUpdateResp, err error)

	AdminBoxLevelList(ctx *gin.Context, req *boxDto.AdminBoxLevelListReq) (res *boxDto.AdminBoxLevelListResp, err error)
}

type BoxQueueLock interface {
	Queue(ctx *gin.Context, req *boxDto.BoxQueueReq) (res *boxDto.BoxQueueRes, err error)

	QueuePush(ctx *gin.Context, req *boxDto.BoxQueuePushReq) (res *boxDto.BoxQueuePushRes, err error)
}

// TODO替换
type Entry struct {
	UserRepo            *user.Entry
	UserService         *userService.Entry
	BoxRecordRepo       box_record.Repo
	BoxLevelRepo        box_level.Repo
	BoxActiveConfigRepo box_active_config.Repo
	ActiveRepo          *activeDao.Entry
	BoxMustConfigRepo   box_must_config.Repo
	BoxQueueLockRepo    box_queue_lock.Repo
	ImageRepo           imageDao.Repo

	UserCardRepo   card_user.Repo
	UserCouponRepo *coupon_user.Entry
	CouponRepo     *coupon.Entry
	CardDayRepo    card_day_send_log.Repo
	CardConfRepo   card_config.Repo
	OrderRepo      orderDao.Repo
	WxPayLogRepo   payLog.Repo
	TradeRepo      orderDetailDao.Repo

	BoxActionDetailRepo box_action_detail.Repo

	BoxGoodsRepo         box_goods.Repo
	BoxGoodsStockLogRepo box_goods_stock_log.Repo

	// 添加商品相关的repository - 用于直购模式
	SkuRepo *sku.Entry
	SpuRepo *spu.Entry

	ConfigRepo configDao.Repo
	CommonSrv  common.Server
	RedisCli   *redis.RedisClient
}

// TODO替换
var (
	// defaultEntry         Server
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

// func GetService() Server {
func GetService() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		UserRepo:             user.GetRepo(),
		UserService:          userService.GetService(),
		BoxRecordRepo:        box_record.GetRepo(),
		BoxLevelRepo:         box_level.GetRepo(),
		ActiveRepo:           activeDao.GetRepo(),
		BoxActiveConfigRepo:  box_active_config.GetRepo(),
		BoxQueueLockRepo:     box_queue_lock.GetRepo(),
		BoxMustConfigRepo:    box_must_config.GetRepo(),
		BoxActionDetailRepo:  box_action_detail.GetRepo(),
		ImageRepo:            imageDao.GetRepo(),
		UserCardRepo:         card_user.GetRepo(),
		UserCouponRepo:       coupon_user.GetRepo(),
		CouponRepo:           coupon.GetRepo(),
		CardDayRepo:          card_day_send_log.GetRepo(),
		CardConfRepo:         card_config.GetRepo(),
		OrderRepo:            orderDao.GetRepo(),
		WxPayLogRepo:         payLog.GetRepo(),
		TradeRepo:            orderDetailDao.GetRepo(),
		BoxGoodsRepo:         box_goods.GetRepo(),
		BoxGoodsStockLogRepo: box_goods_stock_log.GetRepo(),

		SkuRepo: sku.GetRepo(),
		SpuRepo: spu.GetRepo(),

		ConfigRepo: configDao.GetRepo(),
		CommonSrv:  common.GetService(),
		RedisCli:   redis.GetRedisClient(),
	}
}
